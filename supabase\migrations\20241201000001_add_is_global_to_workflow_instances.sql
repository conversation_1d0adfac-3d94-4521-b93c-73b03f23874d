-- =====================================================
-- ADD IS_GLOBAL COLUMN TO WORKFLOW_INSTANCES
-- Supports global workflow configuration
-- =====================================================

-- Add is_global column to workflow_instances table
ALTER TABLE public.workflow_instances 
ADD COLUMN IF NOT EXISTS is_global BOOLEAN DEFAULT false;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_workflow_instances_is_global 
ON public.workflow_instances(is_global);

-- Update RLS policy to include is_global check
DROP POLICY IF EXISTS "workflow_instances_tenant_isolation" ON workflow_instances;

CREATE POLICY "workflow_instances_tenant_isolation" ON workflow_instances
    FOR ALL USING (
        tenant_id = current_setting('app.current_tenant_id')::uuid 
        OR (is_global = true AND tenant_id = current_setting('app.current_tenant_id')::uuid)
    );

-- Add comment for documentation
COMMENT ON COLUMN public.workflow_instances.is_global IS 'Indicates if this is a global workflow (chatbot_id = null)'; 