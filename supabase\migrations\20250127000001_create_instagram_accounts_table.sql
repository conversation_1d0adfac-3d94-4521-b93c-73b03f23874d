-- Migration: Create Instagram Accounts Table for Direct Integration
-- Created: 2025-01-27
-- Purpose: Support direct Instagram Business API integration without Facebook

-- Create instagram_accounts table
CREATE TABLE IF NOT EXISTS instagram_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Instagram Business Account details
    account_id TEXT NOT NULL, -- Instagram Business Account ID from API
    username TEXT NOT NULL,
    account_type TEXT DEFAULT 'BUSINESS' CHECK (account_type IN ('BUSINESS', 'PERSONAL')),
    
    -- Profile information
    name TEXT,
    biography TEXT,
    website TEXT,
    profile_picture_url TEXT,
    
    -- Statistics
    followers_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    media_count INTEGER DEFAULT 0,
    
    -- API tokens and access
    access_token TEXT NOT NULL,
    token_expires_at TIMESTAMPTZ,
    refresh_token TEXT,
    
    -- Account status
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    
    -- Timestamps
    connected_at TIMESTAMPTZ DEFAULT NOW(),
    last_sync_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, account_id), -- One account per tenant
    UNIQUE(account_id) -- Global unique account ID
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_instagram_accounts_tenant_id ON instagram_accounts(tenant_id);
CREATE INDEX IF NOT EXISTS idx_instagram_accounts_account_id ON instagram_accounts(account_id);
CREATE INDEX IF NOT EXISTS idx_instagram_accounts_username ON instagram_accounts(username);
CREATE INDEX IF NOT EXISTS idx_instagram_accounts_active ON instagram_accounts(is_active);
CREATE INDEX IF NOT EXISTS idx_instagram_accounts_connected_at ON instagram_accounts(connected_at);

-- Enable RLS
ALTER TABLE instagram_accounts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "instagram_accounts_tenant_select" ON instagram_accounts
    FOR SELECT USING (tenant_id = auth.get_tenant_id());

CREATE POLICY "instagram_accounts_tenant_insert" ON instagram_accounts
    FOR INSERT WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "instagram_accounts_tenant_update" ON instagram_accounts
    FOR UPDATE USING (tenant_id = auth.get_tenant_id());

CREATE POLICY "instagram_accounts_tenant_delete" ON instagram_accounts
    FOR DELETE USING (tenant_id = auth.get_tenant_id());

-- Create trigger for auto-setting tenant_id
CREATE TRIGGER trigger_instagram_accounts_tenant_id
    BEFORE INSERT ON instagram_accounts
    FOR EACH ROW
    EXECUTE FUNCTION auto_set_tenant_id_simple();

-- Create trigger for updated_at
CREATE TRIGGER trigger_instagram_accounts_updated_at
    BEFORE UPDATE ON instagram_accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create Instagram auto reply config table
CREATE TABLE IF NOT EXISTS instagram_auto_reply_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    account_id TEXT NOT NULL REFERENCES instagram_accounts(account_id) ON DELETE CASCADE,
    
    -- Auto reply settings
    enable_comment_reply BOOLEAN DEFAULT true,
    enable_message_reply BOOLEAN DEFAULT true,
    enable_story_replies BOOLEAN DEFAULT false,
    
    -- Response configuration
    auto_private_reply BOOLEAN DEFAULT false, -- Reply to comments as DM
    reply_tone TEXT DEFAULT 'friendly' CHECK (reply_tone IN ('professional', 'friendly', 'casual', 'formal')),
    reply_language TEXT DEFAULT 'vi' CHECK (reply_language IN ('vi', 'en', 'auto')),
    
    -- AI behavior
    use_business_context BOOLEAN DEFAULT true,
    max_response_length INTEGER DEFAULT 500,
    include_emojis BOOLEAN DEFAULT true,
    
    -- Timing and limits
    response_delay_seconds INTEGER DEFAULT 30,
    max_replies_per_hour INTEGER DEFAULT 50,
    
    -- Working hours (JSON format)
    working_hours JSONB DEFAULT '{"enabled": false, "timezone": "Asia/Ho_Chi_Minh", "hours": {"monday": {"start": "09:00", "end": "17:00"}, "tuesday": {"start": "09:00", "end": "17:00"}, "wednesday": {"start": "09:00", "end": "17:00"}, "thursday": {"start": "09:00", "end": "17:00"}, "friday": {"start": "09:00", "end": "17:00"}, "saturday": {"start": "09:00", "end": "12:00"}, "sunday": {"enabled": false}}}',
    
    -- Blacklist and filters
    ignored_keywords JSONB DEFAULT '[]', -- Keywords to ignore
    blacklisted_users JSONB DEFAULT '[]', -- Usernames to ignore
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, account_id)
);

-- Create indexes for config table
CREATE INDEX IF NOT EXISTS idx_instagram_config_tenant_id ON instagram_auto_reply_config(tenant_id);
CREATE INDEX IF NOT EXISTS idx_instagram_config_account_id ON instagram_auto_reply_config(account_id);
CREATE INDEX IF NOT EXISTS idx_instagram_config_active ON instagram_auto_reply_config(is_active);

-- Enable RLS for config table
ALTER TABLE instagram_auto_reply_config ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for config
CREATE POLICY "instagram_config_tenant_select" ON instagram_auto_reply_config
    FOR SELECT USING (tenant_id = auth.get_tenant_id());

CREATE POLICY "instagram_config_tenant_insert" ON instagram_auto_reply_config
    FOR INSERT WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "instagram_config_tenant_update" ON instagram_auto_reply_config
    FOR UPDATE USING (tenant_id = auth.get_tenant_id());

CREATE POLICY "instagram_config_tenant_delete" ON instagram_auto_reply_config
    FOR DELETE USING (tenant_id = auth.get_tenant_id());

-- Create trigger for auto-setting tenant_id
CREATE TRIGGER trigger_instagram_config_tenant_id
    BEFORE INSERT ON instagram_auto_reply_config
    FOR EACH ROW
    EXECUTE FUNCTION auto_set_tenant_id_simple();

-- Create trigger for updated_at
CREATE TRIGGER trigger_instagram_config_updated_at
    BEFORE UPDATE ON instagram_auto_reply_config
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create Instagram activity logs table
CREATE TABLE IF NOT EXISTS instagram_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    account_id TEXT NOT NULL REFERENCES instagram_accounts(account_id) ON DELETE CASCADE,
    
    -- Activity details
    activity_type TEXT NOT NULL CHECK (activity_type IN (
        'account_connected',
        'account_disconnected',
        'config_updated',
        'comment_replied',
        'message_replied',
        'story_replied',
        'webhook_received',
        'token_refreshed',
        'error_occurred'
    )),
    
    -- Activity metadata
    instagram_post_id TEXT, -- For comment activities
    instagram_comment_id TEXT, -- For comment replies
    instagram_message_id TEXT, -- For message activities
    user_instagram_id TEXT, -- User who triggered the activity
    user_username TEXT,
    
    -- Response details
    original_content TEXT, -- Original comment/message
    ai_response TEXT, -- AI generated response
    response_tone TEXT,
    response_language TEXT,
    
    -- Status and metrics
    status TEXT DEFAULT 'success' CHECK (status IN ('success', 'failed', 'pending')),
    error_message TEXT,
    response_time_ms INTEGER,
    
    -- Additional metadata (JSON)
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes will be created below
);

-- Create indexes for activity logs
CREATE INDEX IF NOT EXISTS idx_instagram_logs_tenant_id ON instagram_activity_logs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_instagram_logs_account_id ON instagram_activity_logs(account_id);
CREATE INDEX IF NOT EXISTS idx_instagram_logs_activity_type ON instagram_activity_logs(activity_type);
CREATE INDEX IF NOT EXISTS idx_instagram_logs_created_at ON instagram_activity_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_instagram_logs_status ON instagram_activity_logs(status);

-- Enable RLS for activity logs
ALTER TABLE instagram_activity_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for activity logs
CREATE POLICY "instagram_logs_tenant_select" ON instagram_activity_logs
    FOR SELECT USING (tenant_id = auth.get_tenant_id());

CREATE POLICY "instagram_logs_tenant_insert" ON instagram_activity_logs
    FOR INSERT WITH CHECK (tenant_id = auth.get_tenant_id());

-- Create trigger for auto-setting tenant_id
CREATE TRIGGER trigger_instagram_logs_tenant_id
    BEFORE INSERT ON instagram_activity_logs
    FOR EACH ROW
    EXECUTE FUNCTION auto_set_tenant_id_simple();

-- Comments
COMMENT ON TABLE instagram_accounts IS 'Direct Instagram Business API integration accounts';
COMMENT ON TABLE instagram_auto_reply_config IS 'Auto reply configuration for Instagram accounts';
COMMENT ON TABLE instagram_activity_logs IS 'Activity logs for Instagram auto reply system';

COMMENT ON COLUMN instagram_accounts.account_id IS 'Instagram Business Account ID from Meta API';
COMMENT ON COLUMN instagram_accounts.access_token IS 'Long-lived access token (60 days)';
COMMENT ON COLUMN instagram_accounts.token_expires_at IS 'Token expiration timestamp';

COMMENT ON COLUMN instagram_auto_reply_config.working_hours IS 'JSON object defining when auto replies are active';
COMMENT ON COLUMN instagram_auto_reply_config.ignored_keywords IS 'Array of keywords to ignore in comments/messages';
COMMENT ON COLUMN instagram_auto_reply_config.blacklisted_users IS 'Array of usernames to ignore'; 