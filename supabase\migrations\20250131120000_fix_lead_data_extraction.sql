-- =====================================================
-- FIX LEAD DATA EXTRACTION MIGRATION
-- Fixes the extraction of contact info from lead_data JSONB field
-- =====================================================

-- =====================================================
-- 1. UPDATE EXISTING RECORDS
-- =====================================================

-- Extract contact info from existing lead_data for records where fields are null
UPDATE public.chatbot_leads 
SET 
    full_name = CASE 
        WHEN full_name IS NULL AND lead_data IS NOT NULL 
        THEN ((lead_data #>> '{}')::jsonb)->>'full_name'
        ELSE full_name
    END,
    email = CASE 
        WHEN email IS NULL AND lead_data IS NOT NULL 
        THEN ((lead_data #>> '{}')::jsonb)->>'email'
        ELSE email
    END,
    phone = CASE 
        WHEN phone IS NULL AND lead_data IS NOT NULL 
        THEN ((lead_data #>> '{}')::jsonb)->>'phone'
        ELSE phone
    END
WHERE lead_data IS NOT NULL 
AND (full_name IS NULL OR email IS NULL OR phone IS NULL);

-- =====================================================
-- 2. IMPROVE TRIGGER FUNCTION
-- =====================================================

-- Create improved trigger function that handles both string and object JSONB
CREATE OR REPLACE FUNCTION extract_lead_contact_info()
RETURNS TRIGGER AS $$
BEGIN
    -- Only extract if lead_data exists and fields are null
    IF NEW.lead_data IS NOT NULL THEN
        -- Handle case where lead_data is stored as JSONB string
        DECLARE
            parsed_data JSONB;
        BEGIN
            -- Try to parse if it's a string, otherwise use as-is
            IF jsonb_typeof(NEW.lead_data) = 'string' THEN
                parsed_data := (NEW.lead_data #>> '{}')::jsonb;
            ELSE
                parsed_data := NEW.lead_data;
            END IF;
            
            -- Extract full_name if not already set
            IF NEW.full_name IS NULL THEN
                NEW.full_name := COALESCE(
                    parsed_data->>'full_name',
                    parsed_data->>'fullName'
                );
            END IF;
            
            -- Extract email if not already set
            IF NEW.email IS NULL THEN
                NEW.email := parsed_data->>'email';
            END IF;
            
            -- Extract phone if not already set
            IF NEW.phone IS NULL THEN
                NEW.phone := parsed_data->>'phone';
            END IF;
            
        EXCEPTION WHEN OTHERS THEN
            -- If parsing fails, log but don't fail the operation
            RAISE WARNING 'Failed to parse lead_data for lead %: %', NEW.id, SQLERRM;
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. RECREATE TRIGGER
-- =====================================================

-- Drop existing trigger if exists
DROP TRIGGER IF EXISTS trigger_extract_lead_contact_info ON public.chatbot_leads;

-- Create new trigger
CREATE TRIGGER trigger_extract_lead_contact_info
    BEFORE INSERT OR UPDATE ON public.chatbot_leads
    FOR EACH ROW
    EXECUTE FUNCTION extract_lead_contact_info();

-- =====================================================
-- 4. ADD HELPFUL INDEXES
-- =====================================================

-- Add indexes for better search performance on extracted fields
CREATE INDEX IF NOT EXISTS idx_chatbot_leads_full_name_search 
ON public.chatbot_leads USING gin(to_tsvector('vietnamese', full_name));

CREATE INDEX IF NOT EXISTS idx_chatbot_leads_email_search 
ON public.chatbot_leads USING gin(to_tsvector('simple', email));

-- =====================================================
-- 5. ADD COMMENTS
-- =====================================================

COMMENT ON FUNCTION extract_lead_contact_info() IS 
'Extracts contact information from lead_data JSONB field into separate columns for better querying and indexing. Handles both string and object JSONB formats.';

COMMENT ON TRIGGER trigger_extract_lead_contact_info ON public.chatbot_leads IS 
'Automatically extracts full_name, email, and phone from lead_data when inserting or updating leads.';

-- =====================================================
-- MIGRATION COMPLETED SUCCESSFULLY
-- ===================================================== 