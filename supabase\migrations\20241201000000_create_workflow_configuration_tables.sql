-- =====================================================
-- WORKFLOW CONFIGURATION TABLES MIGRATION
-- Creates tables for flexible workflow configuration
-- =====================================================

-- =====================================================
-- 1. WORKFLOW TEMPLATES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.workflow_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    
    -- Template Details
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) DEFAULT 'custom', -- sales, support, custom, ecommerce
    is_default BOOLEAN DEFAULT false,
    is_system BOOLEAN DEFAULT false, -- System templates cannot be deleted
    
    -- Workflow Configuration
    stages JSONB NOT NULL DEFAULT '[]',
    transitions JSONB DEFAULT '[]', -- Rules for stage transitions
    notifications JSONB DEFAULT '{}',
    automations JSONB DEFAULT '[]',
    
    -- Visual Configuration
    colors JSONB DEFAULT '{}', -- Stage colors for Kanban
    icons JSONB DEFAULT '{}', -- Stage icons
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_workflow_templates_tenant_id 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);

-- =====================================================
-- 2. WORKFLOW INSTANCES TABLE (Active workflows per chatbot)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.workflow_instances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    chatbot_id UUID,
    template_id UUID,
    
    -- Instance Configuration
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Current Workflow Configuration (can be customized from template)
    stages JSONB NOT NULL DEFAULT '[]',
    transitions JSONB DEFAULT '[]',
    notifications JSONB DEFAULT '{}',
    automations JSONB DEFAULT '[]',
    colors JSONB DEFAULT '{}',
    icons JSONB DEFAULT '{}',
    
    -- Analytics
    total_leads INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,2) DEFAULT 0.00,
    avg_stage_duration JSONB DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_workflow_instances_tenant_id 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_workflow_instances_chatbot_id 
        FOREIGN KEY (chatbot_id) REFERENCES chatbot_configurations(id) ON DELETE SET NULL,
    CONSTRAINT fk_workflow_instances_template_id 
        FOREIGN KEY (template_id) REFERENCES workflow_templates(id) ON DELETE SET NULL
);

-- =====================================================
-- 3. WORKFLOW STAGE TRANSITIONS LOG
-- =====================================================
CREATE TABLE IF NOT EXISTS public.workflow_stage_transitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    workflow_instance_id UUID NOT NULL,
    lead_id UUID NOT NULL,
    
    -- Transition Details
    from_stage VARCHAR(100),
    to_stage VARCHAR(100) NOT NULL,
    transition_type VARCHAR(50) DEFAULT 'manual', -- manual, automatic, rule_based
    
    -- Context
    triggered_by UUID, -- User who triggered the transition
    trigger_rule JSONB, -- Rule that triggered automatic transition
    notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_workflow_transitions_tenant_id 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_workflow_transitions_instance_id 
        FOREIGN KEY (workflow_instance_id) REFERENCES workflow_instances(id) ON DELETE CASCADE,
    CONSTRAINT fk_workflow_transitions_lead_id 
        FOREIGN KEY (lead_id) REFERENCES chatbot_leads(id) ON DELETE CASCADE,
    CONSTRAINT fk_workflow_transitions_triggered_by 
        FOREIGN KEY (triggered_by) REFERENCES users(id) ON DELETE SET NULL
);

-- =====================================================
-- 4. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes for workflow_templates
CREATE INDEX IF NOT EXISTS idx_workflow_templates_tenant_id ON public.workflow_templates(tenant_id);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_category ON public.workflow_templates(category);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_is_active ON public.workflow_templates(is_active);

-- Indexes for workflow_instances
CREATE INDEX IF NOT EXISTS idx_workflow_instances_tenant_id ON public.workflow_instances(tenant_id);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_chatbot_id ON public.workflow_instances(chatbot_id);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_template_id ON public.workflow_instances(template_id);

-- Indexes for workflow_stage_transitions
CREATE INDEX IF NOT EXISTS idx_workflow_transitions_tenant_id ON public.workflow_stage_transitions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_workflow_transitions_instance_id ON public.workflow_stage_transitions(workflow_instance_id);
CREATE INDEX IF NOT EXISTS idx_workflow_transitions_lead_id ON public.workflow_stage_transitions(lead_id);
CREATE INDEX IF NOT EXISTS idx_workflow_transitions_created_at ON public.workflow_stage_transitions(created_at);

-- =====================================================
-- 5. INSERT DEFAULT WORKFLOW TEMPLATES
-- =====================================================

-- Sales workflow template
INSERT INTO public.workflow_templates (
    tenant_id, name, description, category, is_default, is_system, stages, colors, icons
) VALUES (
    '00000000-0000-0000-0000-000000000000', -- System tenant
    'Quy trình Bán hàng Chuẩn',
    'Quy trình chuyển đổi leads thành khách hàng cho các doanh nghiệp bán hàng',
    'sales',
    true,
    true,
    '[
        {
            "id": "new",
            "name": "Lead mới",
            "description": "Lead vừa được tạo từ các kênh marketing",
            "order": 1,
            "isRequired": true,
            "maxDuration": 24,
            "actions": ["contact", "qualify", "assign"]
        },
        {
            "id": "contacted",
            "name": "Đã liên hệ",
            "description": "Đã có liên hệ ban đầu với lead",
            "order": 2,
            "maxDuration": 72,
            "actions": ["follow_up", "qualify", "schedule_meeting"]
        },
        {
            "id": "qualified",
            "name": "Lead tiềm năng",
            "description": "Lead có nhu cầu và khả năng mua hàng",
            "order": 3,
            "maxDuration": 168,
            "actions": ["propose", "negotiate", "quote"]
        },
        {
            "id": "proposal_sent",
            "name": "Đã gửi đề xuất",
            "description": "Đã gửi báo giá hoặc đề xuất cho khách hàng",
            "order": 4,
            "maxDuration": 120,
            "actions": ["follow_up", "negotiate", "close"]
        },
        {
            "id": "converted",
            "name": "Đã chuyển đổi",
            "description": "Lead đã trở thành khách hàng",
            "order": 5,
            "isFinal": true,
            "actions": ["upsell", "referral", "feedback"]
        },
        {
            "id": "lost",
            "name": "Thất bại",
            "description": "Lead không chuyển đổi thành khách hàng",
            "order": 6,
            "isFinal": true,
            "actions": ["nurture", "requalify", "archive"]
        }
    ]'::jsonb,
    '{
        "new": "info",
        "contacted": "warning", 
        "qualified": "primary",
        "proposal_sent": "secondary",
        "converted": "success",
        "lost": "error"
    }'::jsonb,
    '{
        "new": "solar:user-plus-bold",
        "contacted": "solar:phone-bold",
        "qualified": "solar:star-bold", 
        "proposal_sent": "solar:document-text-bold",
        "converted": "solar:check-circle-bold",
        "lost": "solar:close-circle-bold"
    }'::jsonb
);

-- E-commerce workflow template
INSERT INTO public.workflow_templates (
    tenant_id, name, description, category, is_default, is_system, stages, colors, icons
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    'Quy trình E-commerce',
    'Quy trình chuyển đổi từ website visitor thành khách hàng mua hàng',
    'ecommerce', 
    true,
    true,
    '[
        {
            "id": "visitor",
            "name": "Khách truy cập",
            "description": "Khách hàng đang duyệt website",
            "order": 1,
            "isRequired": true,
            "maxDuration": 1,
            "actions": ["engage", "offer_discount", "capture_info"]
        },
        {
            "id": "interested",
            "name": "Quan tâm",
            "description": "Đã thể hiện sự quan tâm đến sản phẩm",
            "order": 2,
            "maxDuration": 24,
            "actions": ["send_info", "offer_demo", "add_to_cart"]
        },
        {
            "id": "cart_abandoned",
            "name": "Bỏ giỏ hàng",
            "description": "Đã thêm sản phẩm vào giỏ nhưng chưa thanh toán",
            "order": 3,
            "maxDuration": 48,
            "actions": ["recovery_email", "offer_discount", "call"]
        },
        {
            "id": "purchased",
            "name": "Đã mua hàng",
            "description": "Đã hoàn thành việc mua hàng",
            "order": 4,
            "isFinal": true,
            "actions": ["upsell", "cross_sell", "review_request"]
        },
        {
            "id": "churned",
            "name": "Rời bỏ",
            "description": "Khách hàng đã rời bỏ không mua hàng",
            "order": 5,
            "isFinal": true,
            "actions": ["win_back", "survey", "retarget"]
        }
    ]'::jsonb,
    '{
        "visitor": "info",
        "interested": "primary",
        "cart_abandoned": "warning",
        "purchased": "success", 
        "churned": "error"
    }'::jsonb,
    '{
        "visitor": "solar:eye-bold",
        "interested": "solar:heart-bold",
        "cart_abandoned": "solar:shopping-cart-bold",
        "purchased": "solar:bag-check-bold",
        "churned": "solar:logout-bold"
    }'::jsonb
);

-- =====================================================
-- 6. CREATE RLS POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE workflow_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_stage_transitions ENABLE ROW LEVEL SECURITY;

-- Workflow Templates Policies
CREATE POLICY "workflow_templates_tenant_isolation" ON workflow_templates
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid OR is_system = true);

-- Workflow Instances Policies  
CREATE POLICY "workflow_instances_tenant_isolation" ON workflow_instances
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

-- Workflow Stage Transitions Policies
CREATE POLICY "workflow_transitions_tenant_isolation" ON workflow_stage_transitions
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

-- =====================================================
-- 7. CREATE FUNCTIONS FOR WORKFLOW MANAGEMENT
-- =====================================================

-- Function to get active workflow for chatbot
CREATE OR REPLACE FUNCTION get_chatbot_workflow(p_chatbot_id UUID)
RETURNS TABLE (
    instance_id UUID,
    template_id UUID,
    stages JSONB,
    colors JSONB,
    icons JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        wi.id as instance_id,
        wi.template_id,
        wi.stages,
        wi.colors,
        wi.icons
    FROM workflow_instances wi
    WHERE wi.chatbot_id = p_chatbot_id 
    AND wi.is_active = true
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to transition lead stage
CREATE OR REPLACE FUNCTION transition_lead_stage(
    p_lead_id UUID,
    p_to_stage VARCHAR(100),
    p_triggered_by UUID DEFAULT NULL,
    p_notes TEXT DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
    v_current_stage VARCHAR(100);
    v_workflow_instance_id UUID;
    v_tenant_id UUID;
    v_result JSONB;
BEGIN
    -- Get current lead info
    SELECT cl.status, wi.id, cl.tenant_id
    INTO v_current_stage, v_workflow_instance_id, v_tenant_id
    FROM chatbot_leads cl
    LEFT JOIN workflow_instances wi ON wi.chatbot_id = cl.chatbot_id AND wi.is_active = true
    WHERE cl.id = p_lead_id;
    
    IF v_current_stage IS NULL THEN
        RETURN '{"success": false, "error": "Lead not found"}'::jsonb;
    END IF;
    
    -- Update lead status
    UPDATE chatbot_leads 
    SET status = p_to_stage, updated_at = NOW()
    WHERE id = p_lead_id;
    
    -- Log transition
    IF v_workflow_instance_id IS NOT NULL THEN
        INSERT INTO workflow_stage_transitions (
            tenant_id, workflow_instance_id, lead_id, 
            from_stage, to_stage, triggered_by, notes
        ) VALUES (
            v_tenant_id, v_workflow_instance_id, p_lead_id,
            v_current_stage, p_to_stage, p_triggered_by, p_notes
        );
    END IF;
    
    RETURN '{"success": true, "previous_stage": "' || v_current_stage || '", "new_stage": "' || p_to_stage || '"}'::jsonb;
END;
$$ LANGUAGE plpgsql; 