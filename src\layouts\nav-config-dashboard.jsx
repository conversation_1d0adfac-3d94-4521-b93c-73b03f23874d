'use client';

import { useMemo } from 'react';

import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/global-config';

import { Label } from 'src/components/label';
import { SvgColor } from 'src/components/svg-color';

// ----------------------------------------------------------------------

const icon = (name) => <SvgColor src={`${CONFIG.assetsDir}/assets/icons/navbar/${name}.svg`} />;

const ICONS = {
  // Core business icons
  job: icon('ic-job'),
  blog: icon('ic-blog'),
  chat: icon('ic-chat'),
  mail: icon('ic-mail'),
  user: icon('ic-user'),
  file: icon('ic-file'),
  lock: icon('ic-lock'),
  tour: icon('ic-tour'),
  order: icon('ic-order'),
  label: icon('ic-label'),
  blank: icon('ic-blank'),
  kanban: icon('ic-kanban'),
  folder: icon('ic-folder'),
  course: icon('ic-course'),
  banking: icon('ic-banking'),
  booking: icon('ic-booking'),
  invoice: icon('ic-invoice'),
  product: icon('ic-product'),
  calendar: icon('ic-calendar'),
  disabled: icon('ic-disabled'),
  external: icon('ic-external'),
  menuItem: icon('ic-menu-item'),
  ecommerce: icon('ic-ecommerce'),
  analytics: icon('ic-analytics'),
  dashboard: icon('ic-dashboard'),
  parameter: icon('ic-parameter'),
  setting: icon('ic-setting'),
  robot: icon('ic-robot'),
  automation: icon('ic-parameter'), // Icon cho tự động hóa
  integration: icon('ic-external'), // Icon cho tích hợp đa kênh
  facebook: icon('ic-facebook'), // Icon cho Facebook Integration
};

// ----------------------------------------------------------------------

/**
 * Input nav data is an array of navigation section items used to define the structure and content of a navigation bar.
 * Each section contains a subheader and an array of items, which can include nested children items.
 *
 * Each item can have the following properties:
 * - `title`: The title of the navigation item.
 * - `path`: The URL path the item links to.
 * - `icon`: An optional icon component to display alongside the title.
 * - `info`: Optional additional information to display, such as a label.
 * - `allowedRoles`: An optional array of roles that are allowed to see the item.
 * - `caption`: An optional caption to display below the title.
 * - `children`: An optional array of nested navigation items.
 * - `disabled`: An optional boolean to disable the item.
 */
export const navData = [
  /**
   * 🤖 AI CHATBOT - Tính năng chính
   */
  {
    subheader: '🤖 AI Chatbot',
    items: [
      {
        title: 'Chatbot AI',
        path: paths.dashboard.moolyChatbot.chatbots,
        icon: ICONS.robot,
        info: (
          <Label color="success" variant="filled">
            HOT
          </Label>
        ),
      },
      {
        title: 'Kênh bán hàng',
        path: paths.dashboard.moolyChatbot.channels,
        icon: ICONS.chat,
      },
      {
        title: 'Tài khoản Credit',
        path: paths.dashboard.credits,
        icon: ICONS.banking,
      },
    ],
  },

  /**
   * 📦 BÁN HÀNG - Quản lý hàng ngày
   */
  {
    subheader: '📦 Bán hàng',
    items: [
      {
        title: 'Đơn hàng',
        path: paths.dashboard.moolyChatbot.orders.root,
        icon: ICONS.order,
        children: [
          { title: 'Tất cả đơn hàng', path: paths.dashboard.moolyChatbot.orders.root },
          { title: 'Thống kê đơn hàng', path: `${paths.dashboard.moolyChatbot.orders.root}/dashboard` },
        ],
        info: (
          <Label color="error" variant="filled">
            MỚI
          </Label>
        ),
      },
      {
        title: 'Sản phẩm',
        path: paths.dashboard.moolyChatbot.products,
        icon: ICONS.product,
        children: [
          { title: 'Danh sách sản phẩm', path: paths.dashboard.moolyChatbot.products },
          { title: 'Danh mục', path: paths.dashboard.moolyChatbot.categories },
          { title: 'Tồn kho', path: paths.dashboard.moolyChatbot.inventory.root },
        ],
      },
      {
        title: 'Khách hàng',
        path: paths.dashboard.moolyChatbot.customers.root,
        icon: ICONS.user,
      },
      {
        title: 'Leads',
        path: paths.dashboard.moolyChatbot.leads.root,
        icon: ICONS.user,
        info: (
          <Label color="success" variant="soft">
            CRM
          </Label>
        ),
      },
    ],
  },

  /**
   * 📊 BÁO CÁO - Phân tích dữ liệu
   */
  {
    subheader: '📊 Báo cáo',
    items: [
      {
        title: 'Thống kê bán hàng',
        path: paths.dashboard.moolyChatbot.analytics,
        icon: ICONS.analytics,
      },
      // {
      //   title: 'Phân tích thông minh',
      //   path: paths.dashboard.moolyChatbot.businessIntelligence,
      //   icon: ICONS.dashboard,
      //   info: (
      //     <Label color="success" variant="soft">
      //       AI
      //     </Label>
      //   ),
      // },
    ],
  },

  /**
   * ⚙️ CÀI ĐẶT - Thiết lập hệ thống
   */
  {
    subheader: '⚙️ Cài đặt',
    items: [
      {
        title: 'Cài đặt chung',
        path: paths.dashboard.moolyChatbot.businessSetup,
        icon: ICONS.setting,
        children: [
          // { title: 'Thông tin doanh nghiệp', path: paths.dashboard.moolyChatbot.businessSetup },
          { title: 'Khuyến mãi', path: paths.dashboard.moolyChatbot.promotions },
          { title: 'Vận chuyển', path: paths.dashboard.moolyChatbot.shipping },
        ],
      },
      {
        title: 'Tự động hóa AI',
        path: paths.dashboard.moolyChatbot.automationRules,
        icon: ICONS.automation,
        info: (
          <Label color="info" variant="soft">
            SẮP RA
          </Label>
        ),
      },
      // {
      //   title: 'Facebook Integration',
      //   path: paths.dashboard.moolyChatbot.facebookIntegration,
      //   icon: ICONS.facebook,
      //   info: (
      //     <Label color="success" variant="soft">
      //       MỚI
      //     </Label>
      //   ),
      // },
      // {
      //   title: 'Social Media Integration',
      //   path: paths.dashboard.moolyChatbot.socialMediaIntegration,
      //   icon: ICONS.integration,
      //   info: (
      //     <Label color="success" variant="soft">
      //       BETA
      //     </Label>
      //   ),
      // },
      // {
      //   title: 'Tích hợp đa kênh',
      //   path: paths.dashboard.moolyChatbot.multiChannelIntegration,
      //   icon: ICONS.integration,
      //   info: (
      //     <Label color="info" variant="soft">
      //       SẮP RA
      //     </Label>
      //   ),
      // },
    ],
  },
];

// ----------------------------------------------------------------------

/**
 * Optimized Navigation Hook
 * Provides memoized navigation data for consistent performance
 * Eliminates loading states and reload issues
 */
export function useOptimizedNavData() {
  // Memoize navigation data to prevent unnecessary re-renders
  // This ensures consistent performance across page reloads
  const memoizedNavData = useMemo(() => navData, []);

  return {
    navData: memoizedNavData,
    loading: false, // Always false since we use static config
    error: null,
    isReady: true // Always ready since no async operations
  };
}

/**
 * Get navigation data synchronously
 * Use this when you need immediate access to nav data
 */
export function getNavData() {
  return navData;
}

/**
 * Navigation data validator
 * Ensures navigation structure is valid
 */
export function validateNavData(data = navData) {
  if (!Array.isArray(data)) {
    console.warn('Navigation data must be an array');
    return false;
  }

  for (const section of data) {
    if (!section.subheader || !Array.isArray(section.items)) {
      console.warn('Invalid navigation section:', section);
      return false;
    }
  }

  return true;
}
