'use client';

import { useState, useCallback, useRef, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';

import { updateProductById } from 'src/app/actions/weaviate/products';
import { useProduct, useProducts } from 'src/actions/mooly-chatbot/product-hooks';
import { useProductApiService } from 'src/actions/mooly-chatbot/product-api-service';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';
import { ConfirmDialog } from 'src/components/custom-dialog';
import {
  useBusinessConfigContext,
  // BusinessAwareDashboard,
  // BusinessAwareQuickActions
} from 'src/components/business-aware';

import { useAuthContext } from 'src/auth/hooks';

import ProductList from './product-list';
import { PlatformSyncDialog } from '../platform-sync';
import ProductCreateDialog from './product-create-dialog';

// ----------------------------------------------------------------------

export function ProductsView() {
  const settings = useSettingsContext();
  const { user } = useAuthContext();
  const tenantId = user?.app_metadata?.tenant_id || '';
  const { businessType, isFeatureEnabled } = useBusinessConfigContext();

  // Always call isFeatureEnabled to avoid conditional hook calls
  const isPlatformSyncEnabled = isFeatureEnabled('platformSync');

  // Dialog states
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [openCreateVariableDialog, setOpenCreateVariableDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openMultiDeleteDialog, setOpenMultiDeleteDialog] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState(null);
  const [selectedProductIds, setSelectedProductIds] = useState([]);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Debounce ref để tránh spam click
  const toggleActiveTimeoutRef = useRef(null);

  // Always call useProduct hook to avoid conditional hook calls
  const { product } = useProduct(selectedProductId);

  const { products, isLoading, mutate } = useProducts();

  // New API service with RLS protection
  const { deleteProduct: deleteProductNew, bulkDeleteProducts } = useProductApiService();

  // Cleanup timeout khi component unmount
  useEffect(() => () => {
    if (toggleActiveTimeoutRef.current) {
      clearTimeout(toggleActiveTimeoutRef.current);
    }
  }, []);

  // Xử lý khi tồn kho được cập nhật
  const handleInventoryUpdated = useCallback(() => {
    // Tải lại danh sách sản phẩm
    mutate();
  }, [mutate]);

  // Hàm xử lý bật/tắt sản phẩm
  const handleToggleActive = useCallback(
    async (productId, newActiveState) => {
      // Debounce để tránh spam click
      if (toggleActiveTimeoutRef.current) {
        clearTimeout(toggleActiveTimeoutRef.current);
      }

      if (isUpdating) return;

      try {
        setIsUpdating(true);
        const loadingToast = toast.loading('Đang cập nhật trạng thái sản phẩm...');

        // 1. Optimistic update - cập nhật UI ngay lập tức
        const optimisticUpdate = (currentData) => {
          if (!currentData) return currentData;
          return currentData.map(item =>
            item.id === productId
              ? { ...item, isActive: newActiveState }
              : item
          );
        };

        // Cập nhật cache ngay lập tức để UI phản hồi nhanh
        mutate(optimisticUpdate, false);

        // 2. Cập nhật trạng thái trong database sử dụng supabase-utils
        const { updateData } = await import('src/actions/mooly-chatbot/supabase-utils');
        const updateResult = await updateData(
          'products',
          { isActive: newActiveState }, // sử dụng camelCase, supabase-utils sẽ tự động chuyển đổi
          { id: productId },
          true
        );

        if (!updateResult.success) {
          // Rollback optimistic update nếu có lỗi
          mutate();
          throw new Error(updateResult.error?.message || 'Không thể cập nhật trạng thái sản phẩm');
        }

        // 3. Đồng bộ với Weaviate (chạy bất đồng bộ để không chặn UI)
        updateProductById({
          tenant_id: tenantId,
          is_active: newActiveState,
          product_id: productId,
        }).catch(weaviateError => {
          console.warn('Cảnh báo: Đồng bộ với Weaviate không thành công', weaviateError);
          // Không hiển thị lỗi cho user vì đây chỉ là đồng bộ phụ
        });

        // 4. Cập nhật UI thành công
        toast.dismiss(loadingToast);
        toast.success(
          `Sản phẩm đã được ${newActiveState ? 'kích hoạt' : 'vô hiệu hóa'} thành công!`
        );

        // 5. Refresh dữ liệu để đảm bảo đồng bộ
        mutate();
      } catch (error) {
        console.error('Error toggling product status:', error);

        // Rollback optimistic update
        mutate();

        // Hiển thị lỗi chi tiết hơn
        const errorMessage = error.message?.includes('row-level security') ||
          error.message?.includes('policy') ||
          error.message?.includes('permission denied')
          ? 'Bạn không có quyền cập nhật trạng thái sản phẩm này'
          : error.message || 'Đã xảy ra lỗi khi cập nhật trạng thái sản phẩm';

        toast.error(errorMessage);
      } finally {
        setIsUpdating(false);
      }
    },
    [isUpdating, tenantId, mutate]
  );

  // Dialog handlers
  const handleOpenCreateDialog = () => {
    setOpenCreateDialog(true);
  };

  const handleOpenCreateVariableDialog = () => {
    setOpenCreateVariableDialog(true);
  };

  const handleCloseCreateDialog = useCallback(() => {
    setOpenCreateDialog(false);
    // Refresh product list after creating
    mutate();
  }, [mutate]);

  const handleCloseCreateVariableDialog = useCallback(() => {
    setOpenCreateVariableDialog(false);
    // Refresh product list after creating
    mutate();
  }, [mutate]);

  const handleOpenEditDialog = useCallback((productId) => {
    setSelectedProductId(productId);
    setOpenEditDialog(true);
  }, []);

  const handleCloseEditDialog = useCallback(() => {
    setOpenEditDialog(false);
    setSelectedProductId(null);
    // Refresh product list after editing
    mutate();
  }, [mutate]);

  const handleOpenDeleteDialog = useCallback((productId) => {
    setSelectedProductId(productId);
    setOpenDeleteDialog(true);
  }, []);

  const handleCloseDeleteDialog = useCallback(() => {
    setOpenDeleteDialog(false);
    setSelectedProductId(null);
  }, []);

  // Xử lý mở dialog xóa nhiều sản phẩm
  const handleOpenMultiDeleteDialog = useCallback((productIds) => {
    setSelectedProductIds(productIds);
    setOpenMultiDeleteDialog(true);
  }, []);

  // Xử lý đóng dialog xóa nhiều sản phẩm
  const handleCloseMultiDeleteDialog = useCallback(() => {
    setOpenMultiDeleteDialog(false);
    setSelectedProductIds([]);
  }, []);

  // Delete product handler
  const handleDeleteProduct = useCallback(async () => {
    if (!selectedProductId || isDeleting) return;

    setIsDeleting(true);
    const loadingToast = toast.loading('Đang xóa sản phẩm...');
    try {
      const result = await deleteProductNew(selectedProductId);

      if (result.success) {
        toast.success('Xóa sản phẩm thành công!');
        // Refresh product list after deleting
        mutate();

        // Reset selected products
        if (typeof window !== 'undefined' && window.resetProductSelection) {
          window.resetProductSelection();
        }

        handleCloseDeleteDialog();
      } else {
        toast.error(`Lỗi: ${result.error?.message || 'Không thể xóa sản phẩm'}`);
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Có lỗi xảy ra khi xóa sản phẩm!');
    } finally {
      toast.dismiss(loadingToast);
      setIsDeleting(false);
    }
  }, [selectedProductId, isDeleting, deleteProductNew, mutate, handleCloseDeleteDialog]);

  // Delete multiple products handler - Sử dụng API mới với RLS protection
  const handleDeleteMultipleProducts = useCallback(
    async (productIds) => {
      if (!productIds || !productIds.length || isDeleting) return;

      setIsDeleting(true);
      const loadingToast = toast.loading(`Đang xóa ${productIds.length} sản phẩm...`);

      try {
        // Sử dụng bulk delete API mới
        const result = await bulkDeleteProducts(productIds);

        if (result.success) {
          const { success_count, failed_count, failed } = result.data;

          // Hiển thị thông báo kết quả
          if (success_count > 0) {
            toast.success(`Đã xóa thành công ${success_count} sản phẩm`);
          }

          if (failed_count > 0) {
            toast.error(`Không thể xóa ${failed_count} sản phẩm`);

            // Log chi tiết các sản phẩm không xóa được
            failed.forEach(item => {
              console.error(`Lỗi khi xóa sản phẩm ${item.name} (${item.id}):`, item.error);
            });
          }

          // Refresh product list
          mutate();

          // Reset selected products
          if (typeof window !== 'undefined' && window.resetProductSelection) {
            window.resetProductSelection();
          }

          handleCloseMultiDeleteDialog();
        } else {
          console.error('Bulk delete failed:', result.error);
          toast.error(`Lỗi: ${result.error?.message || 'Không thể xóa sản phẩm'}`);
        }
      } catch (error) {
        console.error('Error deleting multiple products:', error);
        toast.error('Có lỗi xảy ra khi xóa sản phẩm!');
      } finally {
        toast.dismiss(loadingToast);
        setIsDeleting(false);
      }
    },
    [isDeleting, bulkDeleteProducts, mutate, handleCloseMultiDeleteDialog]
  );

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
        <Typography variant="h4">
          Sản phẩm
          {businessType && (
            <Typography component="span" variant="caption" sx={{ ml: 1, color: 'text.secondary' }}>
              ({businessType === 'retail' ? 'Bán lẻ' :
                businessType === 'digital' ? 'Sản phẩm số' :
                  businessType === 'services' ? 'Dịch vụ' :
                    businessType === 'hybrid' ? 'Đa dạng' : 'Không xác định'})
            </Typography>
          )}
        </Typography>

        <Stack direction="row" spacing={2}>
          <PlatformSyncDialog onSuccess={mutate} />

          <Button
            variant="contained"
            color="primary"
            startIcon={<Iconify icon="eva:plus-fill" />}
            onClick={handleOpenCreateDialog}
          >
            {businessType === 'services' ? 'Thêm dịch vụ mới' :
              businessType === 'digital' ? 'Thêm sản phẩm số mới' :
                'Thêm sản phẩm đơn giản'}
          </Button>

          <Button
            variant="outlined"
            color="warning"
            startIcon={<Iconify icon="eva:options-2-fill" />}
            onClick={handleOpenCreateVariableDialog}
          >
            Thêm sản phẩm có biến thể
          </Button>
        </Stack>
      </Stack>

      {/* Business-Aware Dashboard Widgets - Temporarily disabled */}
      {/* {!businessConfigLoading && (
        <Box sx={{ mb: 3 }}>
          <BusinessAwareDashboard>
            {/* Additional custom widgets can be added here */}
      {/* </BusinessAwareDashboard>
        </Box>
      )} */}

      <Stack direction="row" spacing={3}>
        <Box sx={{ flex: 1 }}>
          <Card>
            <ProductList
              products={products}
              isLoading={isLoading}
              onEdit={handleOpenEditDialog}
              onDelete={handleOpenDeleteDialog}
              onToggleActive={handleToggleActive}
              onDeleteSelected={handleOpenMultiDeleteDialog}
              onInventoryUpdated={handleInventoryUpdated}
            />
          </Card>
        </Box>

        {/* Business-Aware Quick Actions Sidebar - Temporarily disabled */}
        {/* {!businessConfigLoading && (
          <Box sx={{ width: 300, flexShrink: 0 }}>
            <BusinessAwareQuickActions />
          </Box>
        )} */}
      </Stack>

      {/* Create Simple Product Dialog */}
      <ProductCreateDialog
        open={openCreateDialog}
        onClose={handleCloseCreateDialog}
        initialProductType="simple"
      />

      {/* Create Variable Product Dialog */}
      <ProductCreateDialog
        open={openCreateVariableDialog}
        onClose={handleCloseCreateVariableDialog}
        initialProductType="variable"
      />

      {/* Edit Product Dialog */}
      <ProductCreateDialog
        open={openEditDialog}
        onClose={handleCloseEditDialog}
        productId={selectedProductId}
        currentProduct={product}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        title="Xóa sản phẩm"
        content={
          <>
            <Typography variant="body1" gutterBottom>
              Bạn có chắc chắn muốn xóa sản phẩm này?
            </Typography>
            {product && (
              <Box sx={{ mt: 2, p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
                <Stack direction="row" alignItems="center" spacing={2}>
                  {product.avatar && (
                    <Box
                      component="img"
                      src={product.avatar}
                      alt={product.name}
                      sx={{ width: 48, height: 48, borderRadius: 1, objectFit: 'cover' }}
                    />
                  )}
                  <Box>
                    <Typography variant="subtitle2">{product.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Giá:{' '}
                      {new Intl.NumberFormat('vi-VN', {
                        style: 'currency',
                        currency: 'VND',
                      }).format(product.price)}
                    </Typography>
                  </Box>
                </Stack>
              </Box>
            )}
            <Typography variant="body2" color="error.main" sx={{ mt: 2 }}>
              Lưu ý: Hành động này không thể hoàn tác.
            </Typography>
          </>
        }
        action={
          <Stack direction="row" spacing={1}>
            <Button
              variant="outlined"
              color="inherit"
              onClick={handleCloseDeleteDialog}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button
              variant="contained"
              color="error"
              onClick={handleDeleteProduct}
              disabled={isDeleting}
              startIcon={
                isDeleting ? (
                  <Iconify icon="svg-spinners:ring-resize" />
                ) : (
                  <Iconify icon="eva:trash-2-outline" />
                )
              }
            >
              {isDeleting ? 'Đang xóa...' : 'Xóa'}
            </Button>
          </Stack>
        }
      />

      {/* Multiple Delete Confirmation Dialog */}
      <ConfirmDialog
        open={openMultiDeleteDialog}
        onClose={handleCloseMultiDeleteDialog}
        title="Xóa nhiều sản phẩm"
        content={
          <>
            <Typography variant="body1" gutterBottom>
              Bạn có chắc chắn muốn xóa <strong>{selectedProductIds.length}</strong> sản phẩm đã
              chọn?
            </Typography>

            <Box sx={{ mt: 2, p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                Danh sách sản phẩm sẽ bị xóa:
              </Typography>

              <Box sx={{ maxHeight: 200, overflow: 'auto', pr: 1 }}>
                {selectedProductIds.length > 0 && products && (
                  <Stack spacing={1}>
                    {products
                      .filter((p) => selectedProductIds.includes(p.id))
                      .map((p) => (
                        <Box
                          key={p.id}
                          sx={{
                            p: 1,
                            borderRadius: 1,
                            bgcolor: 'background.paper',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                          }}
                        >
                          {p.avatar && (
                            <Box
                              component="img"
                              src={p.avatar}
                              alt={p.name}
                              sx={{ width: 32, height: 32, borderRadius: 0.5, objectFit: 'cover' }}
                            />
                          )}
                          <Typography variant="body2" noWrap sx={{ flex: 1 }}>
                            {p.name}
                          </Typography>
                        </Box>
                      ))}
                  </Stack>
                )}
              </Box>
            </Box>

            <Typography variant="body2" color="error.main" sx={{ mt: 2 }}>
              Lưu ý: Hành động này không thể hoàn tác và sẽ xóa tất cả sản phẩm đã chọn.
            </Typography>
          </>
        }
        action={
          <Stack direction="row" spacing={1}>
            <Button
              variant="contained"
              color="error"
              onClick={() => handleDeleteMultipleProducts(selectedProductIds)}
              disabled={isDeleting}
              startIcon={
                isDeleting ? (
                  <Iconify icon="svg-spinners:ring-resize" />
                ) : (
                  <Iconify icon="eva:trash-2-outline" />
                )
              }
            >
              {isDeleting ? 'Đang xóa...' : 'Xóa tất cả'}
            </Button>
          </Stack>
        }
      />
    </Container>
  );
}
