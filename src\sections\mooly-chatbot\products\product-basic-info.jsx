'use client';

import { useFormContext } from 'react-hook-form';
import { useDebounce } from 'minimal-shared/hooks';
import { memo, useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import IconButton from '@mui/material/IconButton';
import CardContent from '@mui/material/CardContent';
import InputAdornment from '@mui/material/InputAdornment';
import CircularProgress from '@mui/material/CircularProgress';

import { useSKUValidation } from 'src/hooks/use-sku-validation';

import { stringToSlug } from 'src/utils/format-data/string-utils';

import { Iconify } from 'src/components/iconify';
import { Field } from 'src/components/hook-form';
import { MenuItem } from '@mui/material';
import { useCategories } from 'src/actions/mooly-chatbot';

// ----------------------------------------------------------------------

/**
 * Tạo SKU tự động từ tên sản phẩm
 * @param {string} productName - Tên sản phẩm
 * @returns {string} - SKU được tạo tự động
 */
export function generateSKU(productName) {
  if (!productName) return '';

  // Tạo slug từ tên sản phẩm
  const baseSlug = stringToSlug(productName);

  // Thêm timestamp để đảm bảo unique
  const timestamp = Date.now().toString().slice(-6); // Lấy 6 số cuối của timestamp

  // Tạo SKU: SLUG-TIMESTAMP (viết hoa)
  return `${baseSlug}-${timestamp}`.toUpperCase();
}

function ProductBasicInfo() {
  const { watch, setValue } = useFormContext();

  // Theo dõi sự thay đổi của trường name và sku
  const productName = watch('name');
  const currentSKU = watch('sku');

  // Lấy ID sản phẩm hiện tại (nếu đang edit)
  const currentProductId = watch('id');

  // Xác định xem đang ở chế độ edit hay create
  const isEditMode = !!currentProductId;

  // Auto-generate logic
  const [autoGenerateSlug, setAutoGenerateSlug] = useState(!isEditMode);

  const { categories } = useCategories();

  // Sử dụng debounce để tránh cập nhật quá nhanh
  const debouncedProductName = useDebounce(productName, 300);

  // State để theo dõi khi nào cần validate SKU
  const [shouldValidateSKU, setShouldValidateSKU] = useState(false);

  // Sử dụng SKU validation hook - chỉ validate khi cần thiết
  const debouncedSKU = useDebounce(currentSKU, 500);
  const { isValidating, isValid, error } = useSKUValidation(
    shouldValidateSKU ? debouncedSKU : '',
    currentProductId
  );

  // Memoize các hàm để tránh re-render không cần thiết
  const updateSlug = useCallback((name) => {
    if (name && autoGenerateSlug) {
      setValue('slug', stringToSlug(name), { shouldDirty: true });
    }
  }, [autoGenerateSlug, setValue]);

  // Cập nhật slug khi tên sản phẩm thay đổi (với debounce)
  useEffect(() => {
    updateSlug(debouncedProductName);
  }, [debouncedProductName, updateSlug]);

  // Bật validation khi ở create mode hoặc khi user thay đổi SKU trong edit mode
  useEffect(() => {
    if (!isEditMode) {
      // Trong create mode, luôn validate
      setShouldValidateSKU(true);
    }
  }, [isEditMode]);

  // Xử lý khi người dùng thay đổi slug thủ công
  const handleSlugChange = useCallback((event) => {
    // Cập nhật giá trị slug
    setValue('slug', event.target.value, { shouldDirty: true });
    setAutoGenerateSlug(false);
  }, [setValue]);

  // Xử lý khi người dùng muốn tự động tạo lại slug
  const handleRegenerateSlug = useCallback(() => {
    if (productName) {
      setValue('slug', stringToSlug(productName), { shouldDirty: true });
      setAutoGenerateSlug(true);
    }
  }, [productName, setValue]);

  // Xử lý khi người dùng thay đổi SKU thủ công
  const handleSKUChange = useCallback((event) => {
    // Cập nhật giá trị SKU
    setValue('sku', event.target.value, { shouldDirty: true });
    // Bật validation khi user thay đổi SKU
    setShouldValidateSKU(true);
  }, [setValue]);

  // Xử lý khi người dùng muốn tự động tạo lại SKU
  const handleRegenerateSKU = useCallback(() => {
    if (productName) {
      setValue('sku', generateSKU(productName), { shouldDirty: true });
      setShouldValidateSKU(true);
    }
  }, [productName, setValue]);

  return (
    <Card>
      <CardHeader title="Thông tin sản phẩm" />

      <CardContent>
        <Stack spacing={2}>
          <Field.Text
            name="name"
            label="Tên sản phẩm"
            placeholder="Nhập tên sản phẩm"
            required
            size="small"
          />

          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
              gap: 2,
            }}
          >
            <Field.Text
              name="slug"
              label="Slug"
              placeholder="ten-san-pham"
              size="small"
              onChange={handleSlugChange}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleRegenerateSlug}
                        edge="end"
                        title="Tạo lại slug từ tên sản phẩm"
                        size="small"
                      >
                        <Iconify icon="solar:refresh-bold" width={16} />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
            />

            <Field.Text
              name="sku"
              label="Mã SKU"
              placeholder={isEditMode ? "Mã SKU hiện tại" : "Để trống để tự động tạo SKU"}
              size="small"
              onChange={handleSKUChange}
              error={!!error}
              helperText={
                error ||
                (isValidating ? 'Đang kiểm tra SKU...' : '') ||
                (isEditMode ? 'SKU sẽ được kiểm tra khi lưu sản phẩm' : 'Để trống để tự động tạo SKU từ tên sản phẩm')
              }
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {isValidating ? (
                        <CircularProgress size={16} />
                      ) : (
                        <>
                          {isValid && currentSKU && (
                            <Iconify
                              icon="eva:checkmark-circle-2-fill"
                              width={16}
                              sx={{ color: 'success.main', mr: 0.5 }}
                            />
                          )}
                          <IconButton
                            onClick={handleRegenerateSKU}
                            edge="end"
                            title="Tạo lại SKU từ tên sản phẩm"
                            size="small"
                          >
                            <Iconify icon="solar:refresh-bold" width={16} />
                          </IconButton>
                        </>
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
          </Box>

          <Field.Select
            name="categoryId"
            label="Danh mục"
            required
            placeholder="Chọn danh mục"
            size="small"
          >
            <MenuItem value="">-- Không có danh mục cha --</MenuItem>
            {categories.map((category) => (
              <MenuItem key={category.id} value={category.id}>
                {category.name}
              </MenuItem>
            ))}
          </Field.Select>

          <Field.Text
            name="shortDescription"
            label="Mô tả ngắn"
            placeholder="Nhập mô tả ngắn"
            multiline
            rows={2}
            size="small"
          />

          <Stack spacing={1}>
            <Typography variant="subtitle2">Mô tả chi tiết</Typography>
            <Field.Editor
              name="description"
              placeholder="Nhập mô tả chi tiết sản phẩm"
              sx={{ height: 200 }}
            />
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );
}

export default memo(ProductBasicInfo);
