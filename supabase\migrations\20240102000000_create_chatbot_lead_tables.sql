-- =====================================================
-- CHATBOT LEAD CONFIGURATION MIGRATION
-- Creates tables for lead generation and collection
-- =====================================================

-- =====================================================
-- 1. CHATBOT LEAD CONFIGURATIONS TABLE
-- =====================================================

-- Create chatbot_lead_configs table
CREATE TABLE IF NOT EXISTS public.chatbot_lead_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    chatbot_id UUID NOT NULL,
    
    -- Lead Configuration
    is_enabled BOOLEAN DEFAULT false,
    trigger_message TEXT DEFAULT 'Để tôi có thể hỗ trợ bạn tốt hơn, vui lòng cung cấp thông tin liên hệ.',
    success_message TEXT DEFAULT 'Cảm ơn bạn đã cung cấp thông tin! Chúng tôi sẽ liên hệ với bạn sớm nhất.',
    
    -- Collection Settings
    collect_after_messages INTEGER DEFAULT 3,
    max_attempts INTEGER DEFAULT 2,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_chatbot_lead_configs_tenant_id 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_chatbot_lead_configs_chatbot_id 
        FOREIGN KEY (chatbot_id) REFERENCES chatbot_configurations(id) ON DELETE CASCADE,
        
    -- Unique constraint: one config per chatbot
    CONSTRAINT uk_chatbot_lead_configs_chatbot_id UNIQUE (chatbot_id)
);

-- =====================================================
-- 2. CHATBOT LEAD FIELDS TABLE
-- =====================================================

-- Create chatbot_lead_fields table
CREATE TABLE IF NOT EXISTS public.chatbot_lead_fields (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    config_id UUID NOT NULL,
    
    -- Field Configuration
    field_name VARCHAR(100) NOT NULL, -- full_name, email, phone, address, custom_field_1, etc.
    field_label TEXT NOT NULL, -- "Họ và tên", "Email", "Số điện thoại", etc.
    field_type VARCHAR(50) DEFAULT 'text', -- text, email, phone, select, textarea
    field_options JSONB DEFAULT '{}', -- For select fields: {"options": ["Option 1", "Option 2"]}
    
    -- Validation Rules
    is_required BOOLEAN DEFAULT false,
    validation_pattern TEXT, -- Regex pattern for validation
    validation_message TEXT, -- Custom validation error message
    
    -- Display Settings
    display_order INTEGER DEFAULT 1,
    placeholder_text TEXT,
    help_text TEXT,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_chatbot_lead_fields_tenant_id 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_chatbot_lead_fields_config_id 
        FOREIGN KEY (config_id) REFERENCES chatbot_lead_configs(id) ON DELETE CASCADE,
        
    -- Constraints
    CHECK (display_order >= 1 AND display_order <= 100),
    CHECK (field_type IN ('text', 'email', 'phone', 'select', 'textarea', 'number', 'date'))
);

-- =====================================================
-- 3. CHATBOT LEADS TABLE
-- =====================================================

-- Create chatbot_leads table
CREATE TABLE IF NOT EXISTS public.chatbot_leads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    chatbot_id UUID NOT NULL,
    config_id UUID NOT NULL,
    
    -- Lead Information
    lead_data JSONB NOT NULL DEFAULT '{}', -- Stores all collected field data
    conversation_id TEXT, -- Reference to conversation where lead was collected
    
    -- Lead Status
    status VARCHAR(50) DEFAULT 'new', -- new, contacted, qualified, converted, lost
    source VARCHAR(100), -- website, facebook, zalo, etc.
    
    -- Contact Information (extracted from lead_data for easy querying)
    full_name TEXT,
    email TEXT,
    phone TEXT,
    
    -- Follow-up
    notes TEXT,
    assigned_to UUID, -- Reference to user who handles this lead
    last_contact_at TIMESTAMPTZ,
    next_follow_up_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_chatbot_leads_tenant_id 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_chatbot_leads_chatbot_id 
        FOREIGN KEY (chatbot_id) REFERENCES chatbot_configurations(id) ON DELETE CASCADE,
    CONSTRAINT fk_chatbot_leads_config_id 
        FOREIGN KEY (config_id) REFERENCES chatbot_lead_configs(id) ON DELETE CASCADE,
    CONSTRAINT fk_chatbot_leads_assigned_to 
        FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
        
    -- Constraints
    CHECK (status IN ('new', 'contacted', 'qualified', 'converted', 'lost'))
);

-- =====================================================
-- 4. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes for chatbot_lead_configs
CREATE INDEX IF NOT EXISTS idx_chatbot_lead_configs_tenant_id ON public.chatbot_lead_configs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_lead_configs_chatbot_id ON public.chatbot_lead_configs(chatbot_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_lead_configs_is_enabled ON public.chatbot_lead_configs(is_enabled);

-- Indexes for chatbot_lead_fields
CREATE INDEX IF NOT EXISTS idx_chatbot_lead_fields_tenant_id ON public.chatbot_lead_fields(tenant_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_lead_fields_config_id ON public.chatbot_lead_fields(config_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_lead_fields_is_active ON public.chatbot_lead_fields(is_active);
CREATE INDEX IF NOT EXISTS idx_chatbot_lead_fields_display_order ON public.chatbot_lead_fields(display_order);

-- Indexes for chatbot_leads
CREATE INDEX IF NOT EXISTS idx_chatbot_leads_tenant_id ON public.chatbot_leads(tenant_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_leads_chatbot_id ON public.chatbot_leads(chatbot_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_leads_config_id ON public.chatbot_leads(config_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_leads_status ON public.chatbot_leads(status);
CREATE INDEX IF NOT EXISTS idx_chatbot_leads_email ON public.chatbot_leads(email);
CREATE INDEX IF NOT EXISTS idx_chatbot_leads_phone ON public.chatbot_leads(phone);
CREATE INDEX IF NOT EXISTS idx_chatbot_leads_created_at ON public.chatbot_leads(created_at);
CREATE INDEX IF NOT EXISTS idx_chatbot_leads_assigned_to ON public.chatbot_leads(assigned_to);

-- =====================================================
-- 5. ENABLE ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS for all tables
ALTER TABLE public.chatbot_lead_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chatbot_lead_fields ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chatbot_leads ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 6. CREATE RLS POLICIES
-- =====================================================

-- Policies for chatbot_lead_configs
CREATE POLICY "Users can view their tenant's chatbot lead configs" ON public.chatbot_lead_configs
    FOR SELECT
    USING (tenant_id = auth.get_tenant_id());

CREATE POLICY "Users can insert chatbot lead configs for their tenant" ON public.chatbot_lead_configs
    FOR INSERT
    WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "Users can update their tenant's chatbot lead configs" ON public.chatbot_lead_configs
    FOR UPDATE
    USING (tenant_id = auth.get_tenant_id())
    WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "Users can delete their tenant's chatbot lead configs" ON public.chatbot_lead_configs
    FOR DELETE
    USING (tenant_id = auth.get_tenant_id());

-- Policies for chatbot_lead_fields
CREATE POLICY "Users can view their tenant's chatbot lead fields" ON public.chatbot_lead_fields
    FOR SELECT
    USING (tenant_id = auth.get_tenant_id());

CREATE POLICY "Users can insert chatbot lead fields for their tenant" ON public.chatbot_lead_fields
    FOR INSERT
    WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "Users can update their tenant's chatbot lead fields" ON public.chatbot_lead_fields
    FOR UPDATE
    USING (tenant_id = auth.get_tenant_id())
    WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "Users can delete their tenant's chatbot lead fields" ON public.chatbot_lead_fields
    FOR DELETE
    USING (tenant_id = auth.get_tenant_id());

-- Policies for chatbot_leads
CREATE POLICY "Users can view their tenant's chatbot leads" ON public.chatbot_leads
    FOR SELECT
    USING (tenant_id = auth.get_tenant_id());

CREATE POLICY "Users can insert chatbot leads for their tenant" ON public.chatbot_leads
    FOR INSERT
    WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "Users can update their tenant's chatbot leads" ON public.chatbot_leads
    FOR UPDATE
    USING (tenant_id = auth.get_tenant_id())
    WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "Users can delete their tenant's chatbot leads" ON public.chatbot_leads
    FOR DELETE
    USING (tenant_id = auth.get_tenant_id());

-- =====================================================
-- 7. CREATE TRIGGERS FOR AUTO-SETTING TENANT_ID
-- =====================================================

-- Use existing auto_set_tenant_id_simple function for all tables
CREATE TRIGGER trigger_auto_set_tenant_id_chatbot_lead_configs
    BEFORE INSERT ON public.chatbot_lead_configs
    FOR EACH ROW
    EXECUTE FUNCTION auto_set_tenant_id_simple();

CREATE TRIGGER trigger_auto_set_tenant_id_chatbot_lead_fields
    BEFORE INSERT ON public.chatbot_lead_fields
    FOR EACH ROW
    EXECUTE FUNCTION auto_set_tenant_id_simple();

CREATE TRIGGER trigger_auto_set_tenant_id_chatbot_leads
    BEFORE INSERT ON public.chatbot_leads
    FOR EACH ROW
    EXECUTE FUNCTION auto_set_tenant_id_simple();

-- Function to extract contact info from lead_data
CREATE OR REPLACE FUNCTION extract_lead_contact_info()
RETURNS TRIGGER AS $$
BEGIN
    -- Extract full_name from lead_data
    NEW.full_name := NEW.lead_data->>'full_name';
    
    -- Extract email from lead_data
    NEW.email := NEW.lead_data->>'email';
    
    -- Extract phone from lead_data
    NEW.phone := NEW.lead_data->>'phone';
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for extracting contact info
CREATE TRIGGER trigger_extract_lead_contact_info
    BEFORE INSERT OR UPDATE ON public.chatbot_leads
    FOR EACH ROW
    EXECUTE FUNCTION extract_lead_contact_info();

-- =====================================================
-- 8. CREATE DEFAULT LEAD FIELDS FUNCTION
-- =====================================================

-- Function to create default lead fields for a config
CREATE OR REPLACE FUNCTION create_default_lead_fields(config_id_param UUID, tenant_id_param UUID)
RETURNS VOID AS $$
BEGIN
    -- Insert default fields
    INSERT INTO public.chatbot_lead_fields (
        tenant_id, config_id, field_name, field_label, field_type, 
        is_required, display_order, placeholder_text
    ) VALUES
    (tenant_id_param, config_id_param, 'full_name', 'Họ và tên', 'text', true, 1, 'Nhập họ và tên của bạn'),
    (tenant_id_param, config_id_param, 'email', 'Email', 'email', true, 2, 'Nhập địa chỉ email của bạn'),
    (tenant_id_param, config_id_param, 'phone', 'Số điện thoại', 'phone', true, 3, 'Nhập số điện thoại của bạn'),
    (tenant_id_param, config_id_param, 'address', 'Địa chỉ', 'textarea', false, 4, 'Nhập địa chỉ của bạn (tùy chọn)');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 9. CREATE UPDATED_AT TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create updated_at triggers
CREATE TRIGGER trigger_update_chatbot_lead_configs_updated_at
    BEFORE UPDATE ON public.chatbot_lead_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_chatbot_lead_fields_updated_at
    BEFORE UPDATE ON public.chatbot_lead_fields
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_chatbot_leads_updated_at
    BEFORE UPDATE ON public.chatbot_leads
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- MIGRATION COMPLETED SUCCESSFULLY
-- =====================================================

-- Add comment to track migration
COMMENT ON TABLE public.chatbot_lead_configs IS 'Chatbot lead generation configuration table - stores settings for lead collection per chatbot';
COMMENT ON TABLE public.chatbot_lead_fields IS 'Chatbot lead fields configuration table - defines what information to collect from leads';
COMMENT ON TABLE public.chatbot_leads IS 'Chatbot leads data table - stores actual lead information collected from conversations'; 