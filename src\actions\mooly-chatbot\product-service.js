/**
 * <PERSON><PERSON><PERSON> tổng hợp các dịch vụ liên quan đến sản phẩm
 * Cung cấp các API, hooks, và tiện ích để quản lý sản phẩm
 */

// Re-export từ các module con
export * from './product-api';
export * from './product-hooks';
export * from './product-mutations';
export * from './product-constants';
export * from './product-variant-service';
export * from './product-attribute-service';
export * from './product-enhancement-service';
export * from './product-enhancement-hooks';
export * from './product-bulk-operations';

// Import các hàm cần thiết để export riêng
import { getProducts } from './product-api';
import { useProduct, useProducts } from './product-hooks';
import { useProductMutations } from './product-mutations';
import { useProductApiService } from './product-api-service';

// Export các hàm chính để sử dụng trực tiếp
export { useProduct, useProducts, getProducts, useProductMutations, useProductApiService };
