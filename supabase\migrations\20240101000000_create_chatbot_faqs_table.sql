-- Create chatbot_faqs table
CREATE TABLE IF NOT EXISTS public.chatbot_faqs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    chatbot_id UUID NOT NULL,
    topic TEXT NOT NULL,
    content TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_chatbot_faqs_tenant_id 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_chatbot_faqs_chatbot_id 
        FOREIGN KEY (chatbot_id) REFERENCES chatbots(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_chatbot_faqs_tenant_id ON public.chatbot_faqs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_faqs_chatbot_id ON public.chatbot_faqs(chatbot_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_faqs_is_active ON public.chatbot_faqs(is_active);
CREATE INDEX IF NOT EXISTS idx_chatbot_faqs_created_at ON public.chatbot_faqs(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE public.chatbot_faqs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for chatbot_faqs
-- Policy for SELECT: Users can only see FAQs of their tenant
CREATE POLICY "Users can view their tenant's chatbot FAQs" ON public.chatbot_faqs
    FOR SELECT
    USING (tenant_id = get_user_tenant_id());

-- Policy for INSERT: Users can only insert FAQs for their tenant
CREATE POLICY "Users can insert chatbot FAQs for their tenant" ON public.chatbot_faqs
    FOR INSERT
    WITH CHECK (tenant_id = get_user_tenant_id());

-- Policy for UPDATE: Users can only update FAQs of their tenant
CREATE POLICY "Users can update their tenant's chatbot FAQs" ON public.chatbot_faqs
    FOR UPDATE
    USING (tenant_id = get_user_tenant_id())
    WITH CHECK (tenant_id = get_user_tenant_id());

-- Policy for DELETE: Users can only delete FAQs of their tenant
CREATE POLICY "Users can delete their tenant's chatbot FAQs" ON public.chatbot_faqs
    FOR DELETE
    USING (tenant_id = get_user_tenant_id());

-- Create trigger to auto-set tenant_id on INSERT
CREATE OR REPLACE FUNCTION set_chatbot_faqs_tenant_id()
RETURNS TRIGGER AS $$
BEGIN
    -- Auto-set tenant_id if not provided
    IF NEW.tenant_id IS NULL THEN
        NEW.tenant_id = get_user_tenant_id();
    END IF;
    
    -- Validate that user has access to this tenant
    IF NEW.tenant_id != get_user_tenant_id() THEN
        RAISE EXCEPTION 'Access denied: Invalid tenant_id';
    END IF;
    
    -- Auto-set timestamps
    NEW.updated_at = NOW();
    IF TG_OP = 'INSERT' THEN
        NEW.created_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
CREATE TRIGGER trigger_set_chatbot_faqs_tenant_id
    BEFORE INSERT OR UPDATE ON public.chatbot_faqs
    FOR EACH ROW
    EXECUTE FUNCTION set_chatbot_faqs_tenant_id();

-- Add comments for documentation
COMMENT ON TABLE public.chatbot_faqs IS 'Store frequently asked questions for chatbots';
COMMENT ON COLUMN public.chatbot_faqs.id IS 'Unique identifier for the FAQ';
COMMENT ON COLUMN public.chatbot_faqs.tenant_id IS 'Reference to the tenant that owns this FAQ';
COMMENT ON COLUMN public.chatbot_faqs.chatbot_id IS 'Reference to the chatbot this FAQ belongs to';
COMMENT ON COLUMN public.chatbot_faqs.topic IS 'The topic or question of the FAQ';
COMMENT ON COLUMN public.chatbot_faqs.content IS 'The answer or content of the FAQ';
COMMENT ON COLUMN public.chatbot_faqs.is_active IS 'Whether this FAQ is active and should be used by the chatbot';
COMMENT ON COLUMN public.chatbot_faqs.created_at IS 'Timestamp when the FAQ was created';
COMMENT ON COLUMN public.chatbot_faqs.updated_at IS 'Timestamp when the FAQ was last updated'; 