-- Create Facebook Integration Tables
-- Migration: 20250127000000_create_facebook_integration_tables.sql
-- <PERSON><PERSON> dụng chuẩn pattern RLS và trigger của hệ thống

-- Social Media Accounts table (unified for Facebook, Instagram, and future platforms)
CREATE TABLE social_media_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,

    -- Platform information
    platform VARCHAR(50) NOT NULL CHECK (platform IN ('facebook', 'instagram', 'tiktok', 'youtube')),
    account_type VARCHAR(50) NOT NULL CHECK (account_type IN ('page', 'business', 'creator', 'personal')),

    -- Account identifiers
    platform_account_id VARCHAR(255) NOT NULL, -- page_id for Facebook, business_account_id for Instagram
    account_name VARCHAR(255) NOT NULL,
    username VA<PERSON>HA<PERSON>(255),

    -- Visual information
    avatar_url TEXT,
    cover_photo_url TEXT,

    -- Access tokens and authentication
    access_token TEXT NOT NULL,
    page_access_token TEXT, -- For Facebook pages
    user_access_token TEXT,
    token_expires_at TIMESTAMPTZ,

    -- Connected Instagram account (for Facebook pages)
    connected_instagram_id VARCHAR(255),
    connected_instagram_username VARCHAR(255),

    -- Account status
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    follower_count INTEGER DEFAULT 0,

    -- Timestamps
    connected_at TIMESTAMPTZ DEFAULT now(),
    last_sync_at TIMESTAMPTZ DEFAULT now(),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),

    UNIQUE(tenant_id, platform, platform_account_id)
);

-- Facebook Auto Reply Configuration table
CREATE TABLE facebook_auto_reply_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    page_id VARCHAR(255) NOT NULL,
    
    -- Enable/Disable flags
    enable_comment_reply BOOLEAN DEFAULT false,
    enable_message_reply BOOLEAN DEFAULT false,
    enable_instagram_comments BOOLEAN DEFAULT false,
    enable_instagram_messages BOOLEAN DEFAULT false,
    auto_private_reply BOOLEAN DEFAULT false,
    
    -- AI Configuration
    reply_prompt TEXT DEFAULT '',
    reply_tone VARCHAR(50) DEFAULT 'friendly',
    reply_language VARCHAR(10) DEFAULT 'vi',
    max_reply_length INTEGER DEFAULT 500,
    
    -- Business Information for AI Context
    business_info TEXT DEFAULT '',
    products TEXT DEFAULT '',
    policies TEXT DEFAULT '',
    exclude_keywords JSONB DEFAULT '[]',
    
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    UNIQUE(tenant_id, page_id)
);

-- Facebook Activity Logs table
CREATE TABLE facebook_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    page_id VARCHAR(255) NOT NULL,
    activity VARCHAR(100) NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX idx_facebook_accounts_tenant ON facebook_accounts(tenant_id);
CREATE INDEX idx_facebook_accounts_page ON facebook_accounts(page_id);
CREATE INDEX idx_facebook_accounts_instagram ON facebook_accounts(instagram_account_id);
CREATE INDEX idx_facebook_accounts_token_expires ON facebook_accounts(token_expires_at);

CREATE INDEX idx_facebook_config_tenant ON facebook_auto_reply_config(tenant_id);
CREATE INDEX idx_facebook_config_page ON facebook_auto_reply_config(page_id);

CREATE INDEX idx_facebook_logs_tenant ON facebook_activity_logs(tenant_id);
CREATE INDEX idx_facebook_logs_page ON facebook_activity_logs(page_id);
CREATE INDEX idx_facebook_logs_activity ON facebook_activity_logs(activity);
CREATE INDEX idx_facebook_logs_created ON facebook_activity_logs(created_at DESC);

-- Enable Row Level Security theo chuẩn hệ thống
ALTER TABLE facebook_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE facebook_auto_reply_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE facebook_activity_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies theo pattern chuẩn của hệ thống sử dụng auth.get_tenant_id()
-- facebook_accounts
CREATE POLICY "facebook_accounts_tenant_select" ON facebook_accounts
    FOR SELECT USING (tenant_id = auth.get_tenant_id());

CREATE POLICY "facebook_accounts_tenant_insert" ON facebook_accounts
    FOR INSERT WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "facebook_accounts_tenant_update" ON facebook_accounts
    FOR UPDATE USING (tenant_id = auth.get_tenant_id())
    WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "facebook_accounts_tenant_delete" ON facebook_accounts
    FOR DELETE USING (tenant_id = auth.get_tenant_id());

-- facebook_auto_reply_config
CREATE POLICY "facebook_auto_reply_config_tenant_select" ON facebook_auto_reply_config
    FOR SELECT USING (tenant_id = auth.get_tenant_id());

CREATE POLICY "facebook_auto_reply_config_tenant_insert" ON facebook_auto_reply_config
    FOR INSERT WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "facebook_auto_reply_config_tenant_update" ON facebook_auto_reply_config
    FOR UPDATE USING (tenant_id = auth.get_tenant_id())
    WITH CHECK (tenant_id = auth.get_tenant_id());

CREATE POLICY "facebook_auto_reply_config_tenant_delete" ON facebook_auto_reply_config
    FOR DELETE USING (tenant_id = auth.get_tenant_id());

-- facebook_activity_logs
CREATE POLICY "facebook_activity_logs_tenant_select" ON facebook_activity_logs
    FOR SELECT USING (tenant_id = auth.get_tenant_id());

CREATE POLICY "facebook_activity_logs_tenant_insert" ON facebook_activity_logs
    FOR INSERT WITH CHECK (tenant_id = auth.get_tenant_id());

-- Sử dụng trigger function có sẵn để auto-set tenant_id
CREATE TRIGGER trigger_facebook_accounts_tenant_id
    BEFORE INSERT ON facebook_accounts
    FOR EACH ROW
    EXECUTE FUNCTION auto_set_tenant_id_simple();

CREATE TRIGGER trigger_facebook_config_tenant_id
    BEFORE INSERT ON facebook_auto_reply_config
    FOR EACH ROW
    EXECUTE FUNCTION auto_set_tenant_id_simple();

CREATE TRIGGER trigger_facebook_logs_tenant_id
    BEFORE INSERT ON facebook_activity_logs
    FOR EACH ROW
    EXECUTE FUNCTION auto_set_tenant_id_simple();

-- Update triggers cho updated_at (sử dụng pattern có sẵn)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_facebook_accounts_updated_at
    BEFORE UPDATE ON facebook_accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_facebook_config_updated_at
    BEFORE UPDATE ON facebook_auto_reply_config
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Functions for token management
CREATE OR REPLACE FUNCTION get_expiring_facebook_tokens(days_ahead INTEGER DEFAULT 7)
RETURNS TABLE (
    account_id UUID,
    tenant_id UUID,
    page_id VARCHAR,
    page_name VARCHAR,
    expires_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        fa.id,
        fa.tenant_id,
        fa.page_id,
        fa.page_name,
        fa.token_expires_at
    FROM facebook_accounts fa
    WHERE fa.is_active = true
    AND fa.token_expires_at IS NOT NULL
    AND fa.token_expires_at <= (now() + INTERVAL '1 day' * days_ahead)
    ORDER BY fa.token_expires_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get Facebook config with defaults
CREATE OR REPLACE FUNCTION get_facebook_config_with_defaults(p_tenant_id UUID, p_page_id VARCHAR)
RETURNS TABLE (
    enable_comment_reply BOOLEAN,
    enable_message_reply BOOLEAN,
    enable_instagram_comments BOOLEAN,
    enable_instagram_messages BOOLEAN,
    auto_private_reply BOOLEAN,
    reply_prompt TEXT,
    reply_tone VARCHAR,
    reply_language VARCHAR,
    max_reply_length INTEGER,
    business_info TEXT,
    products TEXT,
    policies TEXT,
    exclude_keywords JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(config.enable_comment_reply, false),
        COALESCE(config.enable_message_reply, false),
        COALESCE(config.enable_instagram_comments, false),
        COALESCE(config.enable_instagram_messages, false),
        COALESCE(config.auto_private_reply, false),
        COALESCE(config.reply_prompt, ''),
        COALESCE(config.reply_tone, 'friendly'),
        COALESCE(config.reply_language, 'vi'),
        COALESCE(config.max_reply_length, 500),
        COALESCE(config.business_info, ''),
        COALESCE(config.products, ''),
        COALESCE(config.policies, ''),
        COALESCE(config.exclude_keywords, '[]'::jsonb)
    FROM facebook_auto_reply_config config
    WHERE config.tenant_id = p_tenant_id
    AND config.page_id = p_page_id
    UNION ALL
    SELECT 
        false, false, false, false, false, 
        '', 'friendly', 'vi', 500, '', '', '', '[]'::jsonb
    WHERE NOT EXISTS (
        SELECT 1 FROM facebook_auto_reply_config 
        WHERE tenant_id = p_tenant_id AND page_id = p_page_id
    )
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_facebook_accounts_tenant_active 
    ON facebook_accounts(tenant_id, is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_facebook_logs_tenant_page_created 
    ON facebook_activity_logs(tenant_id, page_id, created_at DESC);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON facebook_accounts TO authenticated;
GRANT ALL ON facebook_auto_reply_config TO authenticated;
GRANT ALL ON facebook_activity_logs TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_expiring_facebook_tokens(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_facebook_config_with_defaults(UUID, VARCHAR) TO authenticated; 