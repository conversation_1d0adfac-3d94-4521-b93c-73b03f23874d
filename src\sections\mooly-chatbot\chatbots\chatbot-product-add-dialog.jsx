'use client';

import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import { TextField } from '@mui/material';
import Checkbox from '@mui/material/Checkbox';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import { alpha, useTheme } from '@mui/material/styles';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import InputAdornment from '@mui/material/InputAdornment';
import TableContainer from '@mui/material/TableContainer';
import TablePagination from '@mui/material/TablePagination';
import CircularProgress from '@mui/material/CircularProgress';

import { useProducts } from 'src/actions/mooly-chatbot/product-service';
import { useChatbotProducts, addProductsToChatbot, getProductsNotInChatbot, isProductInChatbot, invalidateChatbotProductsCache } from 'src/actions/mooly-chatbot/chatbot-product-service';
import { getBotTypeProductConfig } from 'src/actions/mooly-chatbot/chatbot-product-filter-service';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { EmptyContent } from 'src/components/empty-content';

import { useAuthContext } from 'src/auth/hooks';

import BotTypeProductInfo from './components/bot-type-product-info';

// ----------------------------------------------------------------------

export default function ChatbotProductAddDialog({ open, onClose, chatbot, onSuccess }) {
  const theme = useTheme();
  const { user } = useAuthContext();

  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [tabValue, setTabValue] = useState(0);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Lấy cấu hình lọc sản phẩm theo bot type
  const botType = chatbot?.type || 'sale_bot';
  const productConfig = getBotTypeProductConfig(botType);

  // Lấy danh sách sản phẩm theo bot type
  const { products: allProducts, isLoading, error } = useProducts({
    filters: {
      is_active: true,
      ...productConfig.filter,
    },
  });

  // Lấy danh sách sản phẩm đã được thêm vào chatbot
  const { products: chatbotProducts } = useChatbotProducts(chatbot?.id);

  // Reset state khi dialog mở
  useEffect(() => {
    if (open) {
      setSearchQuery('');
      setPage(0);
      setSelectedProducts([]);
      setTabValue(0);
    }
  }, [open]);

  // Xử lý khi thay đổi tab
  const handleChangeTab = (event, newValue) => {
    setTabValue(newValue);
    setPage(0);
  };

  // Xử lý khi thay đổi trang
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Xử lý khi thay đổi số hàng mỗi trang
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Xử lý khi thay đổi truy vấn tìm kiếm
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
    setPage(0);
  };

  // Xử lý khi chọn/bỏ chọn sản phẩm
  const handleSelectProduct = (productId) => {
    setSelectedProducts((prev) => {
      if (prev.includes(productId)) {
        return prev.filter((id) => id !== productId);
      }
      return [...prev, productId];
    });
  };

  // Xử lý khi chọn/bỏ chọn tất cả sản phẩm trên trang hiện tại
  const handleSelectAllProducts = () => {
    if (selectedProducts.length === paginatedProducts.length) {
      setSelectedProducts((prev) =>
        prev.filter((id) => !paginatedProducts.some((product) => product.id === id))
      );
    } else {
      setSelectedProducts((prev) => {
        const newSelected = [...prev];
        paginatedProducts.forEach((product) => {
          if (!newSelected.includes(product.id)) {
            newSelected.push(product.id);
          }
        });
        return newSelected;
      });
    }
  };

  // Xử lý khi thêm sản phẩm vào chatbot
  const handleAddProducts = async () => {
    if (selectedProducts.length === 0) {
      toast.error('Vui lòng chọn ít nhất một sản phẩm');
      return;
    }

    try {
      setIsSubmitting(true);

      // Sử dụng service mới để thêm sản phẩm vào chatbot
      const result = await addProductsToChatbot(
        chatbot.id,
        selectedProducts,
        user?.app_metadata?.tenant_id
      );

      // Invalidate cache để refresh UI
      invalidateChatbotProductsCache(chatbot?.id);

      if (result.count > 0) {
        toast.success(`Đã thêm ${result.count} sản phẩm vào chatbot thành công`);
      } else {
        toast.info('Tất cả sản phẩm đã có trong chatbot');
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('Error adding products to chatbot:', err);
      toast.error(err.message || 'Có lỗi xảy ra khi thêm sản phẩm vào chatbot');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Lọc sản phẩm theo từ khóa tìm kiếm và tab hiện tại
  const filteredProducts = allProducts
    ?.filter((product) => {
      // Lọc theo tab
      if (tabValue === 1) {
        // Tab "Đã thêm vào chatbot"
        return isProductInChatbot(chatbot?.id, product.id, chatbotProducts);
      }
      if (tabValue === 2) {
        // Tab "Chưa thêm vào chatbot"
        return !isProductInChatbot(chatbot?.id, product.id, chatbotProducts);
      }
      // Tab "Tất cả sản phẩm"
      return true;
    })
    ?.filter((product) => {
      // Lọc theo từ khóa tìm kiếm
      const query = searchQuery.toLowerCase();
      return (
        product.name.toLowerCase().includes(query) ||
        (product.sku && product.sku.toLowerCase().includes(query)) ||
        (product.description && product.description.toLowerCase().includes(query))
      );
    });

  // Phân trang sản phẩm
  const paginatedProducts =
    filteredProducts?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage) || [];

  // Kiểm tra xem tất cả sản phẩm trên trang hiện tại đã được chọn chưa
  const isAllSelected =
    paginatedProducts.length > 0 &&
    paginatedProducts.every((product) => selectedProducts.includes(product.id));

  // Kiểm tra xem có sản phẩm nào trên trang hiện tại được chọn không
  const isSomeSelected =
    paginatedProducts.length > 0 &&
    paginatedProducts.some((product) => selectedProducts.includes(product.id)) &&
    !isAllSelected;

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxHeight: 'calc(100% - 64px)' },
      }}
    >
      <DialogTitle>Thêm sản phẩm vào chatbot</DialogTitle>

      <DialogContent sx={{ pb: 0 }}>
        <Stack spacing={3}>
          <TextField
            value={searchQuery}
            onChange={handleSearchChange}
            placeholder="Tìm kiếm sản phẩm..."
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
                </InputAdornment>
              ),
            }}
          />

          {/* Thông báo về việc lọc sản phẩm theo bot type */}
          <BotTypeProductInfo botType={botType} />

          <Tabs
            value={tabValue}
            onChange={handleChangeTab}
            sx={{
              px: 2,
              bgcolor: 'background.neutral',
            }}
          >
            <Tab label="Tất cả sản phẩm" />
            <Tab label="Đã thêm vào chatbot" />
            <Tab label="Chưa thêm vào chatbot" />
          </Tabs>

          {isLoading ? (
            <Box sx={{ py: 5, display: 'flex', justifyContent: 'center' }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Box sx={{ py: 5, textAlign: 'center' }}>
              <Typography variant="body1" color="error">
                Có lỗi xảy ra khi tải dữ liệu sản phẩm
              </Typography>
            </Box>
          ) : filteredProducts?.length === 0 ? (
            <EmptyContent
              title="Không có sản phẩm nào"
              description={
                tabValue === 0
                  ? productConfig.emptyMessage
                  : tabValue === 1
                    ? 'Chưa có sản phẩm nào được thêm vào chatbot này'
                    : 'Tất cả sản phẩm phù hợp đã được thêm vào chatbot này'
              }
              sx={{ py: 5 }}
            />
          ) : (
            <Card>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox">
                        <Checkbox
                          indeterminate={isSomeSelected}
                          checked={isAllSelected}
                          onChange={handleSelectAllProducts}
                        />
                      </TableCell>
                      <TableCell>Sản phẩm</TableCell>
                      <TableCell>Mã SKU</TableCell>
                      <TableCell>Giá</TableCell>
                      <TableCell>Trạng thái</TableCell>
                    </TableRow>
                  </TableHead>

                  <TableBody>
                    {paginatedProducts.map((product) => {
                      const isSelected = selectedProducts.includes(product.id);
                      const isAddedToChatbot = isProductInChatbot(chatbot?.id, product.id, chatbotProducts);

                      return (
                        <TableRow
                          key={product.id}
                          hover
                          onClick={() => handleSelectProduct(product.id)}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell padding="checkbox">
                            <Checkbox checked={isSelected} />
                          </TableCell>

                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={2}>
                              <Box
                                component="img"
                                src={product.avatar || '/assets/placeholder.svg'}
                                alt={product.name}
                                sx={{
                                  width: 48,
                                  height: 48,
                                  borderRadius: 1,
                                  objectFit: 'cover',
                                  border: `solid 1px ${theme.palette.divider}`,
                                }}
                              />
                              <Typography variant="body2" noWrap>
                                {product.name}
                              </Typography>
                            </Stack>
                          </TableCell>

                          <TableCell>{product.sku || '-'}</TableCell>

                          <TableCell>
                            {new Intl.NumberFormat('vi-VN', {
                              style: 'currency',
                              currency: 'VND',
                            }).format(product.price)}
                          </TableCell>

                          <TableCell>
                            <Stack spacing={0.5}>
                              {/* Trạng thái chatbot */}
                              <Box
                                sx={{
                                  display: 'inline-flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  px: 1,
                                  borderRadius: 1,
                                  bgcolor: isAddedToChatbot
                                    ? alpha(theme.palette.success.main, 0.1)
                                    : alpha(theme.palette.info.main, 0.1),
                                  color: isAddedToChatbot
                                    ? theme.palette.success.main
                                    : theme.palette.info.main,
                                }}
                              >
                                {isAddedToChatbot ? 'Đã thêm vào chatbot' : 'Chưa thêm vào chatbot'}
                              </Box>
                              
                              {/* Trạng thái sản phẩm */}
                              <Box
                                sx={{
                                  display: 'inline-flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  px: 1,
                                  borderRadius: 1,
                                  fontSize: '0.75rem',
                                  bgcolor: !product.isActive
                                    ? alpha(theme.palette.error.main, 0.1)
                                    : product.trackInventory && product.stockQuantity <= 0
                                    ? alpha(theme.palette.warning.main, 0.1)
                                    : alpha(theme.palette.success.main, 0.1),
                                  color: !product.isActive
                                    ? theme.palette.error.main
                                    : product.trackInventory && product.stockQuantity <= 0
                                    ? theme.palette.warning.main
                                    : theme.palette.success.main,
                                }}
                              >
                                {!product.isActive 
                                  ? 'Ngừng bán'
                                  : product.trackInventory && product.stockQuantity <= 0 
                                  ? 'Hết hàng'
                                  : 'Đang bán'
                                }
                              </Box>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={filteredProducts?.length || 0}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25]}
                labelRowsPerPage="Số hàng mỗi trang:"
                labelDisplayedRows={({ from, to, count }) => `${from}-${to} của ${count}`}
              />
            </Card>
          )}
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" color="inherit" onClick={onClose}>
          Hủy
        </Button>
        <Button
          variant="contained"
          onClick={handleAddProducts}
          disabled={selectedProducts.length === 0 || isSubmitting}
          startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
        >
          {isSubmitting ? 'Đang thêm...' : 'Thêm sản phẩm'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
