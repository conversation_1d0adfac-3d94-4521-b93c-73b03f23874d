-- =====================================================
-- CHATBOT LEAD CONFIG FIELDS JSON MIGRATION
-- Adds fields_config JSON column to chatbot_lead_configs table
-- This simplifies the architecture by storing field configurations as JSON
-- instead of using a separate chatbot_lead_fields table
-- =====================================================

-- Add fields_config column to store field configurations as JSO<PERSON>
ALTER TABLE public.chatbot_lead_configs 
ADD COLUMN IF NOT EXISTS fields_config JSONB DEFAULT '[]'::jsonb;

-- Add index for better performance when querying JSON data
CREATE INDEX IF NOT EXISTS idx_chatbot_lead_configs_fields_config 
ON public.chatbot_lead_configs USING GIN (fields_config);

-- Add comment to document the new column
COMMENT ON COLUMN public.chatbot_lead_configs.fields_config IS 
'JSON array containing field configurations for lead collection. Each field has: id, name, label, type, required, description';

-- =====================================================
-- MIGRATION COMPLETED SUCCESSFULLY
-- ===================================================== 